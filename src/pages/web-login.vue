<route lang="json">
{
	"name": "webLogin",
	"meta": {
		"layout": "empty",
		"title": "系统登录"
	}
}
</route>
<script setup lang="ts">
// 登录表单数据

const model = ref({ tenantName: '', username: '', password: '', captchaVerification: '' })
const userStore = useUserStore()
const { run, loading } = xUseRequest(BIZ_Auth_APIS.login, model.value)
const alertStore = useAlertStore()
const plantStore = usePlantStore()
async function handleLogin() {
	const res = await run()
	if (res.accessToken && res.userinfo) {
		userStore.token = res.accessToken
		userStore.setUserinfo(res.userinfo)
		alertStore.$reset()
		plantStore.refreshList()
		goHome()
	}
}

// 表单提交处理
function handleSubmit(e: Event) {
	e.preventDefault()
	handleLogin()
}

const appName = import.meta.env.VITE_APP_TITLE
</script>

<template>
	<div class="login-page min-h-screen flex items-center justify-center">
		<!-- 动态背景 -->
		<div class="dynamic-background">
			<div class="bg-layer-1"></div>
			<div class="bg-layer-2"></div>
			<div class="bg-layer-3"></div>
			<!-- 科技网格背景 -->
			<div class="tech-grid-bg"></div>
			<!-- 浮动几何图形 -->
			<div class="floating-shapes">
				<div v-for="i in 8" :key="i" class="floating-shape" :style="{ '--delay': i * 0.5 + 's' }"></div>
			</div>
			<!-- 数据流线条 -->
			<div class="data-streams">
				<div v-for="i in 6" :key="i" class="data-stream" :style="{ '--delay': i * 1.2 + 's' }"></div>
			</div>
		</div>

		<div class="login-container relative overflow-hidden">
			<!-- 科技感背景装饰 -->
			<div class="tech-bg-decoration">
				<div class="tech-grid"></div>
				<div class="tech-particles">
					<div v-for="i in 20" :key="i" class="particle"></div>
				</div>
				<!-- 边框扫描线 -->
				<div class="border-scan-lines">
					<div class="scan-line scan-line-top"></div>
					<div class="scan-line scan-line-right"></div>
					<div class="scan-line scan-line-bottom"></div>
					<div class="scan-line scan-line-left"></div>
				</div>
			</div>

			<!-- 登录表单内容 -->
			<div class="login-content">
				<!-- 登录表单标题 -->
				<div class="mb-8 text-center">
					<!-- 科技感图标 -->
					<div class="flex items-center justify-center">
						<div class="tech-icon-container mb-4 mr-base">
							<svg class="tech-icon" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
								<circle cx="32" cy="32" r="30" stroke="url(#gradient1)" stroke-width="2" fill="none" />
								<circle cx="32" cy="32" r="20" stroke="url(#gradient2)" stroke-width="1.5" fill="none" opacity="0.6" />
								<circle cx="32" cy="32" r="10" fill="url(#gradient3)" />
								<path d="M32 12 L36 20 L32 28 L28 20 Z" fill="url(#gradient4)" />
								<path d="M52 32 L44 36 L36 32 L44 28 Z" fill="url(#gradient4)" />
								<path d="M32 52 L28 44 L32 36 L36 44 Z" fill="url(#gradient4)" />
								<path d="M12 32 L20 28 L28 32 L20 36 Z" fill="url(#gradient4)" />
								<defs>
									<linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
										<stop offset="0%" stop-color="#00d4ff" />
										<stop offset="100%" stop-color="#0099cc" />
									</linearGradient>
									<linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
										<stop offset="0%" stop-color="#00ffff" />
										<stop offset="100%" stop-color="#0080ff" />
									</linearGradient>
									<radialGradient id="gradient3" cx="50%" cy="50%" r="50%">
										<stop offset="0%" stop-color="#00d4ff" stop-opacity="0.8" />
										<stop offset="100%" stop-color="#0099cc" stop-opacity="0.3" />
									</radialGradient>
									<linearGradient id="gradient4" x1="0%" y1="0%" x2="100%" y2="100%">
										<stop offset="0%" stop-color="#ffffff" />
										<stop offset="100%" stop-color="#00d4ff" />
									</linearGradient>
								</defs>
							</svg>
						</div>
						<div class="tech-title">{{ appName }}</div>
					</div>
					<div class="tech-divider"></div>
				</div>

				<!-- 登录表单 -->
				<div class="mt-4rem space-y-6">
					<!-- 用户名输入框 -->
					<div class="input-group">
						<label class="tech-label">
							<div class="label-icon i-carbon:account text-lg"></div>
							<span class="text-lg">用户名</span>
						</label>
						<div class="tech-input-wrapper">
							<div class="input-border-effect"></div>
							<input v-model="model.username" placeholder="请输入用户名" class="tech-input" @keyup.enter="handleLogin" />
							<div class="input-glow"></div>
							<div class="input-scan-line"></div>
							<div class="input-corner-effects">
								<div class="corner corner-tl"></div>
								<div class="corner corner-tr"></div>
								<div class="corner corner-bl"></div>
								<div class="corner corner-br"></div>
							</div>
						</div>
					</div>

					<!-- 密码输入框 -->
					<div class="input-group">
						<label class="tech-label">
							<div class="label-icon i-carbon:password text-lg"></div>
							<span class="text-lg">密码</span>
						</label>
						<div class="tech-input-wrapper">
							<div class="input-border-effect"></div>
							<input v-model="model.password" type="password" placeholder="请输入密码" class="tech-input" @keyup.enter="handleLogin" />
							<div class="input-glow"></div>
							<div class="input-scan-line"></div>
							<div class="input-corner-effects">
								<div class="corner corner-tl"></div>
								<div class="corner corner-tr"></div>
								<div class="corner corner-bl"></div>
								<div class="corner corner-br"></div>
							</div>
						</div>
					</div>

					<!-- 登录按钮 -->
					<div class="pt-6">
						<div class="tech-button-wrapper">
							<XButton :loading="loading" :disabled="!model.username || !model.password" class="tech-login-button" @click="handleSubmit">
								<span class="button-text tracking-0.8rem text-lg">{{ loading ? '系统验证中...' : '授权登录' }}</span>
								<div class="button-glow"></div>
								<div class="button-scan-line"></div>
							</XButton>
						</div>
					</div>

					<!-- 系统状态信息 -->
					<div class="tech-status">
						<div class="status-info flex items-center justify-center gap-0.5rem">
							<div class="i-carbon:security color-success"></div>
							<span class="tracking-0.2rem">数据安全连接已建立</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<style scoped>
/* 登录页面整体样式 */
.login-page {
	position: relative;
	overflow: hidden;
}

/* 动态背景样式 */
.dynamic-background {
	position: fixed;
	inset: 0;
	z-index: -1;
	overflow: hidden;
}

.bg-layer-1 {
	position: absolute;
	inset: 0;
	background: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #0f172a 50%, #1e293b 75%, #0f172a 100%);
	animation: bgShift1 20s ease-in-out infinite;
}

.bg-layer-2 {
	position: absolute;
	inset: 0;
	background:
		radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
		radial-gradient(circle at 80% 20%, rgba(0, 153, 204, 0.1) 0%, transparent 50%);
	animation: bgShift2 15s ease-in-out infinite reverse;
}

.bg-layer-3 {
	position: absolute;
	inset: 0;
	background: conic-gradient(
		from 0deg at 50% 50%,
		transparent 0deg,
		rgba(0, 212, 255, 0.05) 90deg,
		transparent 180deg,
		rgba(0, 153, 204, 0.05) 270deg,
		transparent 360deg
	);
	animation: bgRotate 30s linear infinite;
}

@keyframes bgShift1 {
	0%,
	100% {
		transform: translateX(0) translateY(0);
	}

	25% {
		transform: translateX(-2%) translateY(-1%);
	}

	50% {
		transform: translateX(1%) translateY(2%);
	}

	75% {
		transform: translateX(2%) translateY(-1%);
	}
}

@keyframes bgShift2 {
	0%,
	100% {
		transform: scale(1) rotate(0deg);
	}

	50% {
		transform: scale(1.1) rotate(2deg);
	}
}

@keyframes bgRotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.tech-grid-bg {
	position: absolute;
	inset: 0;
	background-image: linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
	0% {
		transform: translate(0, 0);
	}

	100% {
		transform: translate(50px, 50px);
	}
}

.floating-shapes {
	position: absolute;
	inset: 0;
}

.floating-shape {
	position: absolute;
	width: 4px;
	height: 4px;
	background: linear-gradient(45deg, #00d4ff, #0099cc);
	border-radius: 50%;
	animation: floatShape 15s ease-in-out infinite;
	animation-delay: var(--delay);
}

.floating-shape:nth-child(1) {
	left: 10%;
	top: 20%;
}

.floating-shape:nth-child(2) {
	left: 80%;
	top: 10%;
}

.floating-shape:nth-child(3) {
	left: 20%;
	top: 80%;
}

.floating-shape:nth-child(4) {
	left: 90%;
	top: 70%;
}

.floating-shape:nth-child(5) {
	left: 50%;
	top: 30%;
}

.floating-shape:nth-child(6) {
	left: 30%;
	top: 60%;
}

.floating-shape:nth-child(7) {
	left: 70%;
	top: 40%;
}

.floating-shape:nth-child(8) {
	left: 60%;
	top: 90%;
}

@keyframes floatShape {
	0%,
	100% {
		transform: translateY(0) scale(1);
		opacity: 0.3;
	}

	50% {
		transform: translateY(-20px) scale(1.5);
		opacity: 1;
	}
}

.data-streams {
	position: absolute;
	inset: 0;
}

.data-stream {
	position: absolute;
	width: 2px;
	height: 100vh;
	background: linear-gradient(to bottom, transparent, #00d4ff, transparent);
	animation: streamFlow 8s linear infinite;
	animation-delay: var(--delay);
}

.data-stream:nth-child(1) {
	left: 15%;
}

.data-stream:nth-child(2) {
	left: 25%;
}

.data-stream:nth-child(3) {
	left: 45%;
}

.data-stream:nth-child(4) {
	left: 65%;
}

.data-stream:nth-child(5) {
	left: 75%;
}

.data-stream:nth-child(6) {
	left: 85%;
}

@keyframes streamFlow {
	0% {
		transform: translateY(-100vh);
		opacity: 0;
	}

	10% {
		opacity: 1;
	}

	90% {
		opacity: 1;
	}

	100% {
		transform: translateY(100vh);
		opacity: 0;
	}
}

/* 登录容器样式 */
.login-container {
	width: 500px;
	background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 50%, rgba(15, 23, 42, 0.95) 100%);
	border: 1px solid rgba(0, 212, 255, 0.3);
	border-radius: 16px;
	backdrop-filter: blur(20px);
	box-shadow:
		0 0 50px rgba(0, 212, 255, 0.2),
		0 20px 40px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	animation: containerAppear 0.8s ease-out;
	padding: 3rem;
	box-sizing: border-box;
}

@keyframes containerAppear {
	from {
		opacity: 0;
		transform: scale(0.9) translateY(-30px);
	}

	to {
		opacity: 1;
		transform: scale(1) translateY(0);
	}
}

/* 科技感背景装饰 */
.tech-bg-decoration {
	position: absolute;
	inset: 0;
	pointer-events: none;
	z-index: 0;
}

.tech-grid {
	position: absolute;
	inset: 0;
	background-image: linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
	background-size: 20px 20px;
	animation: gridPulse 4s ease-in-out infinite;
}

@keyframes gridPulse {
	0%,
	100% {
		opacity: 0.3;
	}

	50% {
		opacity: 0.6;
	}
}

.tech-particles {
	position: absolute;
	inset: 0;
}

.particle {
	position: absolute;
	width: 0.25rem;
	height: 0.25rem;
	border-radius: 9999px;
	background: linear-gradient(45deg, #00d4ff, #0099cc);
	animation: particleFloat 6s linear infinite;
}

.particle:nth-child(odd) {
	animation-delay: -2s;
}

.particle:nth-child(3n) {
	animation-delay: -4s;
}

@keyframes particleFloat {
	0% {
		transform: translateY(100vh) translateX(0) scale(0);
		opacity: 0;
	}

	10% {
		opacity: 1;
		scale: 1;
	}

	90% {
		opacity: 1;
	}

	100% {
		transform: translateY(-10px) translateX(20px) scale(0);
		opacity: 0;
	}
}

.particle:nth-child(1) {
	left: 10%;
	animation-duration: 8s;
}

.particle:nth-child(2) {
	left: 20%;
	animation-duration: 6s;
}

.particle:nth-child(3) {
	left: 30%;
	animation-duration: 7s;
}

.particle:nth-child(4) {
	left: 40%;
	animation-duration: 9s;
}

.particle:nth-child(5) {
	left: 50%;
	animation-duration: 5s;
}

.particle:nth-child(6) {
	left: 60%;
	animation-duration: 8s;
}

.particle:nth-child(7) {
	left: 70%;
	animation-duration: 6s;
}

.particle:nth-child(8) {
	left: 80%;
	animation-duration: 7s;
}

.particle:nth-child(9) {
	left: 90%;
	animation-duration: 9s;
}

.particle:nth-child(10) {
	left: 15%;
	animation-duration: 5s;
}

.particle:nth-child(11) {
	left: 25%;
	animation-duration: 8s;
}

.particle:nth-child(12) {
	left: 35%;
	animation-duration: 6s;
}

.particle:nth-child(13) {
	left: 45%;
	animation-duration: 7s;
}

.particle:nth-child(14) {
	left: 55%;
	animation-duration: 9s;
}

.particle:nth-child(15) {
	left: 65%;
	animation-duration: 5s;
}

.particle:nth-child(16) {
	left: 75%;
	animation-duration: 8s;
}

.particle:nth-child(17) {
	left: 85%;
	animation-duration: 6s;
}

.particle:nth-child(18) {
	left: 95%;
	animation-duration: 7s;
}

.particle:nth-child(19) {
	left: 5%;
	animation-duration: 9s;
}

.particle:nth-child(20) {
	left: 95%;
	animation-duration: 5s;
}

/* 边框扫描线 */
.border-scan-lines {
	position: absolute;
	inset: 0;
	pointer-events: none;
}

.scan-line {
	position: absolute;
	background: linear-gradient(90deg, transparent, #00d4ff, transparent);
	opacity: 0;
	animation: borderScan 4s ease-in-out infinite;
}

.scan-line-top {
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	animation-delay: 0s;
}

.scan-line-right {
	top: 0;
	right: 0;
	bottom: 0;
	width: 2px;
	background: linear-gradient(to bottom, transparent, #00d4ff, transparent);
	animation-delay: 1s;
}

.scan-line-bottom {
	bottom: 0;
	left: 0;
	right: 0;
	height: 2px;
	animation-delay: 2s;
}

.scan-line-left {
	top: 0;
	left: 0;
	bottom: 0;
	width: 2px;
	background: linear-gradient(to bottom, transparent, #00d4ff, transparent);
	animation-delay: 3s;
}

@keyframes borderScan {
	0%,
	90%,
	100% {
		opacity: 0;
	}

	5%,
	85% {
		opacity: 1;
	}
}

/* 登录内容区域 */
.login-content {
	position: relative;
	z-index: 10;
	color: white;
}

/* 科技感图标 */
.tech-icon-container {
	display: flex;
	justify-content: center;
}

.tech-icon {
	width: 4rem;
	height: 4rem;
	animation: iconRotate 8s linear infinite;
	filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

@keyframes iconRotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

/* 标题样式 */
.tech-title {
	font-size: 1.5rem;
	font-weight: 700;
	margin-bottom: 0.5rem;
	background: linear-gradient(135deg, #ffffff 0%, #00d4ff 50%, #ffffff 100%);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	text-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
	animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
	from {
		filter: brightness(1);
	}

	to {
		filter: brightness(1.2);
	}
}

.tech-divider {
	width: 6rem;
	height: 1px;
	margin: 0 auto 1.5rem;
	background: linear-gradient(90deg, transparent, #00d4ff, transparent);
	animation: dividerPulse 2s ease-in-out infinite;
}

@keyframes dividerPulse {
	0%,
	100% {
		opacity: 0.5;
		transform: scaleX(1);
	}

	50% {
		opacity: 1;
		transform: scaleX(1.2);
	}
}

/* 输入框组样式 */
.input-group {
	position: relative;
}

.tech-label {
	display: flex;
	align-items: center;
	gap: 0.5rem;
	font-size: 0.875rem;
	font-weight: 500;
	color: #67e8f9;
	margin-bottom: 0.75rem;
	letter-spacing: 0.5px;
}

.label-icon {
	width: 1rem;
	height: 1rem;
	color: #00d4ff;
}

.tech-input-wrapper {
	position: relative;
}

/* 输入框边框效果 */
.input-border-effect {
	position: absolute;
	inset: -2px;
	border-radius: 0.5rem;
	background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.3), transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.tech-input-wrapper:focus-within .input-border-effect {
	opacity: 1;
	animation: borderPulse 2s ease-in-out infinite;
}

@keyframes borderPulse {
	0%,
	100% {
		opacity: 0.3;
	}

	50% {
		opacity: 1;
	}
}

:deep(.tech-input) {
	width: 100%;
	height: 3rem;
	padding: 0 1rem;
	color: white;
	background: rgba(15, 23, 42, 0.6);
	border: 2px solid rgba(100, 116, 139, 0.3);
	border-radius: 0.5rem;
	transition: all 0.3s ease;
	outline: none;
	position: relative;
	z-index: 2;
}

:deep(.tech-input:focus) {
	border-color: rgba(0, 212, 255, 0.8) !important;
	box-shadow:
		0 0 0 1px rgba(0, 212, 255, 0.5),
		0 0 20px rgba(0, 212, 255, 0.2),
		inset 0 0 10px rgba(0, 212, 255, 0.1) !important;
	background: rgba(15, 23, 42, 0.9);
}

:deep(.tech-input::placeholder) {
	color: rgba(148, 163, 184, 0.6);
	transition: color 0.3s ease;
}

:deep(.tech-input:focus::placeholder) {
	color: rgba(148, 163, 184, 0.8);
}

.input-glow {
	position: absolute;
	inset: 0;
	border-radius: 0.5rem;
	pointer-events: none;
	background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.tech-input-wrapper:focus-within .input-glow {
	opacity: 1;
	animation: inputScan 2s ease-in-out infinite;
}

@keyframes inputScan {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(100%);
	}
}

/* 输入框扫描线 */
.input-scan-line {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, transparent, #00d4ff, transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.tech-input-wrapper:focus-within .input-scan-line {
	opacity: 1;
	animation: inputScanLine 1.5s ease-in-out infinite;
}

@keyframes inputScanLine {
	0% {
		transform: translateX(-100%);
	}

	100% {
		transform: translateX(100%);
	}
}

/* 输入框角落效果 */
.input-corner-effects {
	position: absolute;
	inset: 0;
	pointer-events: none;
}

.corner {
	position: absolute;
	width: 12px;
	height: 12px;
	border: 2px solid #00d4ff;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.corner-tl {
	top: -2px;
	left: -2px;
	border-right: none;
	border-bottom: none;
}

.corner-tr {
	top: -2px;
	right: -2px;
	border-left: none;
	border-bottom: none;
}

.corner-bl {
	bottom: -2px;
	left: -2px;
	border-right: none;
	border-top: none;
}

.corner-br {
	bottom: -2px;
	right: -2px;
	border-left: none;
	border-top: none;
}

.tech-input-wrapper:focus-within .corner {
	opacity: 1;
	animation: cornerGlow 2s ease-in-out infinite;
}

@keyframes cornerGlow {
	0%,
	100% {
		opacity: 0.5;
		transform: scale(1);
	}

	50% {
		opacity: 1;
		transform: scale(1.1);
	}
}

/* 登录按钮样式 */
.tech-button-wrapper {
	position: relative;
}

:deep(.tech-login-button) {
	position: relative;
	width: 100%;
	height: 3.5rem;
	overflow: hidden;
	background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 153, 204, 0.3) 50%, rgba(0, 212, 255, 0.2) 100%);
	border: 2px solid rgba(0, 212, 255, 0.5);
	border-radius: 8px;
	color: white !important;
	font-weight: 600;
	letter-spacing: 1px;
	transition: all 0.3s ease;
	text-transform: uppercase;
}

:deep(.tech-login-button:hover:not(:disabled)) {
	border-color: #00d4ff;
	box-shadow:
		0 0 30px rgba(0, 212, 255, 0.4),
		inset 0 0 20px rgba(0, 212, 255, 0.1);
	transform: translateY(-2px);
}

:deep(.tech-login-button:disabled) {
	opacity: 0.5;
	cursor: not-allowed;
}

.button-text {
	position: relative;
	z-index: 10;
}

.button-glow {
	position: absolute;
	inset: 0;
	border-radius: 0.5rem;
	background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.2), transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
}

.tech-login-button:hover .button-glow {
	opacity: 1;
	animation: buttonGlow 1.5s ease-in-out infinite;
}

@keyframes buttonGlow {
	0% {
		transform: translateX(-100%) skewX(-15deg);
	}

	100% {
		transform: translateX(100%) skewX(-15deg);
	}
}

.button-scan-line {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 0.125rem;
	background: linear-gradient(90deg, transparent, #00d4ff, transparent);
	opacity: 0;
	animation: scanLine 3s ease-in-out infinite;
}

@keyframes scanLine {
	0%,
	90%,
	100% {
		opacity: 0;
		transform: translateY(0);
	}

	5%,
	85% {
		opacity: 1;
	}

	45% {
		transform: translateY(56px);
	}
}

/* 系统状态样式 */
.tech-status {
	text-align: center;
	padding-top: 1.5rem;
	display: flex;
	flex-direction: column;
	gap: 0.5rem;
}

.status-info {
	font-size: 1rem;
	color: #94a3b8;
}

/* 响应式调整 */
@media (max-width: 640px) {
	.login-container {
		width: 20rem;
		margin: 0 1rem;
		padding: 2rem;
	}

	.tech-title {
		font-size: 1.25rem;
	}
}
</style>
